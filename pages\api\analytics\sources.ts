import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../../lib/ga4Client';

type SourceStat = {
  source: string;
  sessions: number;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SourceStat[]>
) {
  try {
    const { client: analyticsDataClient, propertyId } = createGA4Client();

    const [response] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: '7daysAgo', endDate: 'today' }],
      dimensions: [{ name: 'sessionSource' }],
      metrics: [{ name: 'sessions' }],
      orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
      limit: 5,
    });

    const rows: SourceStat[] = response.rows?.map((row: any) => ({
      source: row.dimensionValues?.[0]?.value ?? '',
      sessions: parseInt(row.metricValues?.[0]?.value ?? '0', 10)
    })) ?? [];

    res.status(200).json(rows);
  } catch (err: unknown) {
    console.error('GA4 API error:', err);
    res.status(500).json([]);
  }
}
