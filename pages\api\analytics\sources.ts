import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../../lib/ga4Client';

type SourceStat = {
  source: string;
  sessions: number;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SourceStat[]>
) {
  // Set a timeout for the entire request
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), 25000); // 25 second timeout
  });

  try {
    const dataPromise = async () => {
      const { client: analyticsDataClient, propertyId } = createGA4Client();

      const [response] = await analyticsDataClient.runReport({
        property: `properties/${propertyId}`,
        dateRanges: [{ startDate: '7daysAgo', endDate: 'today' }],
        dimensions: [{ name: 'sessionSource' }],
        metrics: [{ name: 'sessions' }],
        orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
        limit: 5,
      });

      return response.rows?.map((row: any) => ({
        source: row.dimensionValues?.[0]?.value ?? '',
        sessions: parseInt(row.metricValues?.[0]?.value ?? '0', 10)
      })) ?? [];
    };

    const rows = await Promise.race([dataPromise(), timeoutPromise]);
    res.status(200).json(rows);

  } catch (err: unknown) {
    console.error('GA4 API error (sources):', err);

    // Handle specific error types gracefully
    if (err instanceof Error && (err.message.includes('timeout') || err.message.includes('aborted'))) {
      console.log('Timeout detected, returning empty array');
    }

    // Return empty array for any errors to prevent UI breaking
    res.status(200).json([]);
  }
}
