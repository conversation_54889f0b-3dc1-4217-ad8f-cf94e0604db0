import { NextApiRequest, NextApiResponse } from 'next';
import { format } from 'date-fns-tz';

const TIMEZONE = 'America/Denver';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const USERNAME = process.env.HELP_DESK_USERNAME;
  const PASSWORD = process.env.HELP_DESK_PASSWORD;

  if (!USERNAME || !PASSWORD) {
    console.error("Missing LiveChat credentials");
    return res.status(500).json({ error: "Missing API credentials" });
  }

  const ACCESS_TOKEN = Buffer.from(`${USERNAME}:${PASSWORD}`).toString('base64');

  // Define date range: first and last day of last month
  const now = new Date();
  const firstDayLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const lastDayLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
  lastDayLastMonth.setHours(23, 59, 59, 999);

  const formatForLiveChat = (date: Date) =>
    format(date, "yyyy-MM-dd'T'HH:mm:ssXXX", { timeZone: TIMEZONE });

  const from = formatForLiveChat(firstDayLastMonth);
  const to = formatForLiveChat(lastDayLastMonth);

  try {
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 9000);

    const response = await fetch('https://api.livechatinc.com/v3.5/reports/agents/performance', {
      method: 'POST',
      headers: {
        Authorization: `Basic ${ACCESS_TOKEN}`,
        Accept: 'application/json',
        'X-API-VERSION': '3.5',
        'Content-Type': 'application/json'
      },
      signal: controller.signal,
      body: JSON.stringify({
        filters: { from, to },
        timezone: TIMEZONE
      })
    });

    clearTimeout(timeout);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`LiveChat API error: ${response.status} - ${errorText}`);
      return res.status(response.status).json({ error: errorText });
    }

    const data = await response.json();
    res.status(200).json(data);
  } catch (err: unknown) {
    if ((err as Error).name === 'AbortError') {
      console.error("LiveChat API request timed out");
      return res.status(504).json({ error: "LiveChat API request timed out" });
    }

    console.error("LiveChat error:", (err as Error).message);
    res.status(500).json({ error: "Failed to fetch LiveChat data" });
  }
}
