// components/Loader.tsx
import { useEffect, useState } from 'react';

export default function Loader({ show }: { show: boolean }) {
  const [visible, setVisible] = useState(show);
  const [fadeOut, setFadeOut] = useState(false);

  useEffect(() => {
    if (show) {
      setVisible(true);
      setFadeOut(false);
    } else {
      setFadeOut(true);
      setTimeout(() => setVisible(false), 500); // after fade animation
    }
  }, [show]);

  if (!visible) return null;

  return (
    <div className={`loader-wrapper fixed inset-0 bg-white z-50 flex justify-center items-center transition-opacity duration-500 ${fadeOut ? 'opacity-0' : 'opacity-100'}`}>
      <div className="loader-inner">
        <div className="loader animate-spin h-10 w-10 border-t-2 border-b-2 border-green-500 rounded-full" />
      </div>
    </div>
  );
}
