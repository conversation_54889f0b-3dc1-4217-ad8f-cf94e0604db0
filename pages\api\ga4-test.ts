import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../lib/ga4Client';

// Test endpoint to verify GA4 configuration
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    console.log('[GA4-TEST] Testing GA4 configuration...');
    
    // Check environment variables
    const client_email = process.env.GA4_CLIENT_EMAIL;
    const private_key = process.env.GA4_PRIVATE_KEY;
    const propertyId = process.env.GA4_PROPERTY_ID;
    
    console.log('[GA4-TEST] Environment check:', {
      hasClientEmail: !!client_email,
      hasPrivateKey: !!private_key,
      hasPropertyId: !!propertyId,
      propertyId: propertyId
    });

    if (!client_email || !private_key || !propertyId) {
      return res.status(500).json({ 
        error: 'Missing GA4 environment variables',
        details: {
          hasClientEmail: !!client_email,
          hasPrivateKey: !!private_key,
          hasPropertyId: !!propertyId
        }
      });
    }

    // Test client creation
    const { client, propertyId: testPropertyId } = createGA4Client();
    console.log('[GA4-TEST] Client created successfully for property:', testPropertyId);

    // Test a simple API call with timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Test timeout')), 15000);
    });

    const testPromise = async () => {
      console.log('[GA4-TEST] Making test API call...');
      const [response] = await client.runReport({
        property: `properties/${testPropertyId}`,
        dateRanges: [{ startDate: 'yesterday', endDate: 'yesterday' }],
        metrics: [{ name: 'activeUsers' }],
      });
      
      return {
        success: true,
        hasData: !!response.rows && response.rows.length > 0,
        rowCount: response.rows?.length || 0
      };
    };

    const result = await Promise.race([testPromise(), timeoutPromise]);
    
    console.log('[GA4-TEST] Test completed successfully:', result);
    return res.status(200).json({
      status: 'success',
      message: 'GA4 configuration is working',
      ...result
    });

  } catch (err: unknown) {
    console.error('[GA4-TEST] Test failed:', err);
    
    if (err instanceof Error) {
      return res.status(500).json({
        status: 'error',
        message: err.message,
        type: err.constructor.name
      });
    }
    
    return res.status(500).json({
      status: 'error',
      message: 'Unknown error occurred'
    });
  }
}
