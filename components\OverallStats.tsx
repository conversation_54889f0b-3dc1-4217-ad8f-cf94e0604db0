import { useEffect, useState } from 'react';

type KPI = {
    name: string;
    key: string;
    value: number;
};

type SegAllResponse = {
  lastMonth: { kpis: KPI[] };
  prevMonth: { kpis: KPI[] };
};

export default function OverallStats() {
  const [revenue, setRevenue] = useState(0);
  const [revenueChange, setRevenueChange] = useState(0);
  const [orders, setOrders] = useState(0);
  const [orderChange, setOrderChange] = useState(0);
  const [aovChange, setAovChange] = useState(0);
  const [aov, setAov] = useState(0);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOverall = async () => {
      try {
        const res = await fetch('/api/segall');
        if (!res.ok) throw new Error(`HTTP error! ${res.status}`);
        const data: SegAllResponse = await res.json();

        const getKpiValue = (kpis: KPI[], key: string) => {
          const kpi = kpis.find(k => k.key === key);
          return kpi?.value ?? 0;
        };

        const last = data.lastMonth.kpis;
        const prev = data.prevMonth.kpis;

        const lastRevenue = getKpiValue(last, 'revenue');
        const prevRevenue = getKpiValue(prev, 'revenue');
        const lastOrders = getKpiValue(last, 'numOfOrders');
        const prevOrders = getKpiValue(prev, 'numOfOrders')
        const lastAov = getKpiValue(last, 'aov');
        const prevAov = getKpiValue(prev, 'aov')

        setRevenue(lastRevenue);
        setOrders(lastOrders);
        setAov(lastAov);

        const revenueDelta = prevRevenue === 0 ? 0 : ((lastRevenue - prevRevenue) / prevRevenue) * 100;
        const orderDelta = prevOrders === 0 ? 0 : ((lastOrders - prevOrders) / prevOrders) * 100;
        const aovDelta = prevAov === 0 ? 0 : ((lastAov - prevAov) / prevAov) * 100;

        setRevenueChange(revenueDelta);
        setOrderChange(orderDelta);
        setAovChange(aovDelta);

      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchOverall();
  }, []);

  if (loading) return <div>Loading overall stats...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="overall">
      <div className="row">
        <div className="new-card total-revenue">
          <div className="card-body">
            <p>Total Revenue</p>
            <h3 className="total-rev">
              ${revenue.toLocaleString()}
              <span className={revenueChange >= 0 ? 'increase' : 'decrease'}>
                {revenueChange >= 0 ? '▲' : '▼'} {Math.abs(revenueChange).toFixed(1)}%
              </span>
            </h3>
          </div>
        </div>
        <div className="new-card total-orders">
          <div className="card-body">
            <p>Total Orders</p>
            <h3 className="total-ord">{orders.toLocaleString()}
            <span className={orderChange >= 0 ? 'increase' : 'decrease'}>
                {orderChange >= 0 ? '▲' : '▼'} {Math.abs(orderChange).toFixed(1)}%
              </span>
            </h3>
          </div>
        </div>
        <div className="new-card total-avg-val">
          <div className="card-body">
            <p>Avg Customer Value</p>
            <h3 className="total-avg-value">${aov.toFixed(0)}
            <span className={aovChange >= 0 ? 'increase' : 'decrease'}>
                {aovChange >= 0 ? '▲' : '▼'} {Math.abs(aovChange).toFixed(1)}%
              </span>
            </h3>
          </div>
        </div>
      </div>
      <p className="data-disclaimer">Data is reflective of last month.</p>
    </div>
  );
}