import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../lib/ga4Client';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log('[API] Start handler');

  try {
    const { client, propertyId } = createGA4Client();

    console.log('[API] Fetching real-time data...');
    const [response] = await client.runRealtimeReport({
      property: `properties/${propertyId}`,
      dimensions: [{ name: 'unifiedScreenName' }],
      metrics: [{ name: 'activeUsers' }],
    });

    const totalUsers = response.rows?.reduce((sum, row) => {
      return sum + parseInt(row.metricValues?.[0].value || '0');
    }, 0) ?? 0;

    console.log('[API] Returning:', totalUsers);
    return res.status(200).json({ activeUsers: totalUsers });

  } catch (err) {
    console.error('[API] GA4 error:', err);
    return res.status(500).json({ error: 'GA4 fetch failed' });
  }
}