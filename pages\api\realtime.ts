import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../lib/ga4Client';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log('[API] Start handler');

  // Set a timeout for the entire request
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), 25000); // 25 second timeout
  });

  try {
    const dataPromise = async () => {
      const { client, propertyId } = createGA4Client();

      console.log('[API] Fetching real-time data...');
      const [response] = await client.runRealtimeReport({
        property: `properties/${propertyId}`,
        dimensions: [{ name: 'unifiedScreenName' }],
        metrics: [{ name: 'activeUsers' }],
      });

      return response.rows?.reduce((sum, row) => {
        return sum + parseInt(row.metricValues?.[0].value || '0');
      }, 0) ?? 0;
    };

    const totalUsers = await Promise.race([dataPromise(), timeoutPromise]);
    console.log('[API] Returning:', totalUsers);
    return res.status(200).json({ activeUsers: totalUsers });

  } catch (err) {
    console.error('[API] GA4 error (realtime):', err);

    // Handle timeout gracefully
    if (err instanceof Error && (err.message.includes('timeout') || err.message.includes('aborted'))) {
      console.log('[API] Timeout detected, returning 0 users');
      return res.status(200).json({ activeUsers: 0 });
    }

    // Return 0 users instead of error to prevent UI breaking
    return res.status(200).json({ activeUsers: 0 });
  }
}