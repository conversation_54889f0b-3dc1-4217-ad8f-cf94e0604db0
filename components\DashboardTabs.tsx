import { useState } from 'react';
import ProductivityDashboard from './Productivity';
import WebsiteAnalytics from './Analytics';
import SecurityCompliance from './Security';

export default function DashboardTabs() {
  const [activeTab, setActiveTab] = useState<'productivity' | 'analytics' | 'security'>('productivity');
  const [showOverlay, setShowOverlay] = useState(false);

  const handleTabChange = (tab: typeof activeTab) => {
    if (tab === activeTab) return;
    setActiveTab(tab);
    setShowOverlay(true);

    setTimeout(() => {
      setShowOverlay(false);
    }, 3000); // show overlay for 2s
  };

  return (
    <div className="dashboard-tabs">
      <div className="menu-tabs flex space-x-4 mb-4">
        <button onClick={() => handleTabChange('productivity')} className={activeTab === 'productivity' ? 'tab-active' : 'tab'}>
          Productivity
        </button>
        <button onClick={() => handleTabChange('analytics')} className={activeTab === 'analytics' ? 'tab-active' : 'tab'}>
          Analytics
        </button>
        <button onClick={() => handleTabChange('security')} className={activeTab === 'security' ? 'tab-active' : 'tab'}>
          Security
        </button>
      </div>

      {/* Tab content */}
      <div className="tab-content relative min-h-[300px]">
        {activeTab === 'productivity' && <ProductivityDashboard />}
        {activeTab === 'analytics' && <WebsiteAnalytics />}
        {activeTab === 'security' && <SecurityCompliance />}

        {/* Overlay on top of content */}
        {showOverlay && (
          <div className="dash-loader inset-0 flex items-center justify-center z-10 transition-opacity duration-300">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-green-500"></div>
          </div>
        )}
      </div>
    </div>
  );
}
