import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

  const apiKey = process.env.GOODACCESS_API_KEY;
  if (!apiKey) {
    return res.status(500).json({ error: 'API configuration error' });
  }

  const endpoint = 'https://integration.goodaccess.com/api/v1/gateway-level-logs'; // Example

  try {
    const response = await fetch(endpoint, {
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        "Accept": "*/*"
      },
    });

    if (!response.ok) {
      console.error('GoodAccess API error:', response.status);
      return res.status(500).json({ error: 'Failed to fetch VPN data' });
    }

    const data = await response.json();
    res.status(200).json(data);
  } catch (err) {
    console.error('GoodAccess API error:', err);
    res.status(500).json({ error: 'Failed to fetch VPN data' });
  }
}
