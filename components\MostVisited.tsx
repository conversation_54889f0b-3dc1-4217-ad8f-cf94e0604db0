import { useEffect, useState } from 'react';

type PageStat = {
  path: string;
  views: number;
};

export default function MostVisited() {
  const [pages, setPages] = useState<PageStat[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const controller = new AbortController();

    const fetchPages = async () => {
      try {
        const res = await fetch('/api/analytics/mostVisited', { signal: controller.signal });
        if (!res.ok) throw new Error(`Error ${res.status}`);
        const data: PageStat[] = await res.json();
        setPages(data);
      } catch (err: unknown) {
        if (err instanceof Error) {
            setError(err.message);
        } else {
            setError('An unknown error occurred');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchPages();

    return () => {
      controller.abort(); // Cleanup on unmount to avoid memory leaks
    };
  }, []);

  return (
    <div className="new-card">
      <h4 className="mb-2">Top Pages</h4>

      {loading && <p className="text-gray-500 text-sm">Loading...</p>}
      {error && <p className="text-red-500 text-sm">Error: {error}</p>}

      {!loading && !error && (
        <ul className="text-sm text-gray-700">
          {pages.map((p, i) => (
            <li key={i} className="flex justify-between border-b py-1">
              <span>{p.path}</span>
              <span>{p.views.toLocaleString()} views</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
