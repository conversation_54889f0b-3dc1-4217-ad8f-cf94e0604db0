import { BetaAnalyticsDataClient } from '@google-analytics/data';

export function createGA4Client() {
  const client_email = process.env.GA4_CLIENT_EMAIL;
  const private_key = process.env.GA4_PRIVATE_KEY?.replace(/\\n/g, '\n');
  const propertyId = process.env.GA4_PROPERTY_ID;

  if (!client_email || !private_key || !propertyId) {
    throw new Error('GA4 configuration missing. Please check environment variables.');
  }

  const client = new BetaAnalyticsDataClient({
    credentials: {
      client_email,
      private_key,
    },
  });

  return { client, propertyId };
}
