import { ReactNode } from 'react';

export default function DashboardLayout({ children }: { children: ReactNode }) {
    return (

        <main id='' className="ml-3 mr-3 sm:ml-4 sm:mr-2">
            <section id="hero" className="hero">
                <div className="inner-hero">
                    <h1>Nexus</h1>
                    <p>Unifying Insights, Elevating Performance.</p>
                </div>
            </section>
            <div id='main'>
                <div className="row" id="main-row">
                    {children}
                </div>
            </div>
        </main>
    );    
}

