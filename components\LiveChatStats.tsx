// components/LiveChatStats.tsx
import { useEffect, useState } from 'react';

type LiveChatSummary = {
    chats_count: number;
    chats_rated_good: number;
    first_response_time: number;
};

type LiveChatResponse = {
    summary: LiveChatSummary;
    records: Record<string, { total: number }>;
};

export default function LiveChatStats() {
    const [data, setData] = useState<LiveChatSummary | null>(null);
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchLiveChat = async () => {
            try {
                const res = await fetch('/api/livechat');
                if (!res.ok) throw new Error(`HTTP error! ${res.status}`);
                const json: LiveChatResponse = await res.json();
                setData(json.summary);
            } catch (err: unknown) {
                if (err instanceof Error) {
                  setError(err.message);
                } else {
                  setError('An unknown error occurred');
                }
              } finally {
                setLoading(false);
            }
        };

        fetchLiveChat();
    }, []);

    if (loading) return <div>Loading support stats...</div>;
    if (error) return <div>Error: {error}</div>;
    if (!data) return null;

    return (
        <section className="support-section p-4">
            <h2>Customer Support</h2>
            <p className="data-disclaimer">Data is reflective of the last month.</p>
            <div className="row">
                <div className="new-card col-lg-4">
                    <div className="new-card-body">
                        <p>Total Chats</p>
                        <h3>{data.chats_count.toLocaleString()}</h3>
                    </div>
                </div>
                <div className="new-card col-lg-4">
                    <div className="new-card-body">
                        <p>Positive Rated Chats</p>
                        <h3>{data.chats_rated_good}</h3>
                    </div>
                </div>
                <div className="new-card col-lg-4">
                    <div className="new-card-body">
                        <p>Avg Response Time</p>
                        <h3>{Math.trunc(data.first_response_time)}s</h3>
                    </div>
                </div>
            </div>
        </section>
    );
}
