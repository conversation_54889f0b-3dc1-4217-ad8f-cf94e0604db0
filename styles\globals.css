@import "tailwindcss";

:root {
  --dark: #2f3e46;
  --medium: #354f52;
  --light: #52796f;
  --accent: #84a98c;
}

* {
  font-family: "Quicksand", sans-serif;
  font-weight: 500;
}

/* HTML: <div class="loader"></div> */
.loader {
  width: 4px;
  color: #fff;
  aspect-ratio: 1;
  border-radius: 50%;
  box-shadow: 
    19px -19px 0 0px, 38px -19px 0 0px, 57px -19px 0 0px,
    19px 0     0 5px, 38px 0     0 5px, 57px 0     0 5px,
    19px 19px  0 0px, 38px 19px  0 0px, 57px 19px  0 0px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  animation: l26 2s infinite linear;
}
.fade-out {
  opacity: 0;
}
@keyframes l26 {
  12.5% {box-shadow: 
    19px -19px 0 0px, 38px -19px 0 0px, 57px -19px 0 5px,
    19px 0     0 5px, 38px 0     0 0px, 57px 0     0 5px,
    19px 19px  0 0px, 38px 19px  0 0px, 57px 19px  0 0px}
  25%   {box-shadow: 
    19px -19px 0 5px, 38px -19px 0 0px, 57px -19px 0 5px,
    19px 0     0 0px, 38px 0     0 0px, 57px 0     0 0px,
    19px 19px  0 0px, 38px 19px  0 5px, 57px 19px  0 0px}
  50%   {box-shadow: 
    19px -19px 0 5px, 38px -19px 0 5px, 57px -19px 0 0px,
    19px 0     0 0px, 38px 0     0 0px, 57px 0     0 0px,
    19px 19px  0 0px, 38px 19px  0 0px, 57px 19px  0 5px}
  62.5% {box-shadow: 
    19px -19px 0 0px, 38px -19px 0 0px, 57px -19px 0 0px,
    19px 0     0 5px, 38px 0     0 0px, 57px 0     0 0px,
    19px 19px  0 0px, 38px 19px  0 5px, 57px 19px  0 5px}
  75%   {box-shadow: 
    19px -19px 0 0px, 38px -19px 0 5px, 57px -19px 0 0px,
    19px 0     0 0px, 38px 0     0 0px, 57px 0     0 5px,
    19px 19px  0 0px, 38px 19px  0 0px, 57px 19px  0 5px}
  87.5% {box-shadow: 
    19px -19px 0 0px, 38px -19px 0 5px, 57px -19px 0 0px,
    19px 0     0 0px, 38px 0     0 5px, 57px 0     0 0px,
    19px 19px  0 5px, 38px 19px  0 0px, 57px 19px  0 0px}
}


.loader-inner {
  position: relative;
  height: 100%;
  width: 100%;
}
.loader-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  background: rgba(47, 62, 70, .85);
  backdrop-filter: blur(15px);
  width: 100%;
  height: 100%;
  opacity: 1;
  transition: opacity 0.5s ease;
}

body, html, #__next {
  background: var(--dark);
}

h1, h2, h3, p {
  color: #fff !important
}

.menu-tabs {
    position: absolute;
    right: 20px;
    top: 15px;
}

.menu-tabs button {
  margin: 10px;
  padding: 5px;
  color: var(--accent);
  transition: linear .1s;
}

.menu-tabs button:hover {
  color: #fff;
}

#main {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  margin: auto;
}

#main-row {
  flex-wrap: wrap;
  justify-content: space-evenly;
  width: 100%;
}

.screen {
  margin-top: 30px;
}

.screen h4 {
  color: #fff;
}

.screen .new-card ul {
  color: #fff;
  opacity: .75;
}

.cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.cards .new-card {
  background: none;
  border: none;
  width: 49%;
  background: rgba(255, 255, 255, .1);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 25px;
}

.cards .new-card ul li {
  border-color: rgba(255, 255, 255, .25);
}

.tab-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.left, .right {
  width: 49%
}

#hero {
  width: 100%;
  max-width: 100%;
  padding: 10px 5px;
  border-radius: 0;
  margin: 0;  
  border-bottom: 1px solid var(--medium);
}

.inner-hero {
  margin: auto;
}

#hero p {
  font-size: 12px;
}

#hero p, #hero h1 {
  margin: 0;
}

#main section {
  background: var(--medium);
  border-radius: 8px;
  margin: 15px 0;
  width: 100%;
}

.new-card {
  background: none;
  border: none
}

.new-card-body {
  border: 1px solid var(--light);
  border-radius: 8px;
  padding: 15px;
}

.new-card-body p {
  border-radius: 3px;
  font-size: 13px;
  margin-bottom: 5px;
}

p.data-disclaimer {
  font-size: 14px;
}

.new-card-body h3 {
  font-size: 28px;
  margin: 0;
}

.overall {
  margin-top: 15px;
}

.overall .new-card p {
  padding: 5px 10px 5px 20px;
  font-size: 20px;
  background: var(--light);
  border-radius: 8px 8px 0 0;
}

.overall .new-card {
  background: var(--medium);
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 0;
}

.new-card h3 {
  color: #fff;
  font-weight: 500;
}

.overall .row {
  flex-wrap: wrap;
  justify-content: space-between;
}

#main .total-orders, #main .total-avg-val {
  width: 49%;
}

.total-rev, .total-ord, .total-avg-value {
  font-size: 75px;
  padding: 50px;
}

.overall h3 {
  display: flex;
  align-items: center;
}

.decrease {
  font-size: 18px;
  display: flex;
  min-width: fit-content;
  margin-left: 10px;
  background: #D22B2B;
  color: #fff !important;
  padding: 5px 12px;
  border-radius: 5px;
}

.thirty {
  font-size: 12px;
  margin-left: 5px;
  font-style: italic;
  opacity: .75;
}

.real-time-card {
    padding: 30px;
    border: 1px solid var(--light);
    border-radius: 12px;
    margin-bottom: 30px;
}

.sec-card {
  border: 1px solid var(--light);
  border-radius: 12px;
  padding: 30px;
  margin-top: 30px;
}

.sec-card h4 {
  margin-bottom: 30px;
}

.gateway-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 20px;
}

.gateway-item {
    border: none;
    padding: 10px;
    background: rgba(255, 255, 255, .1);
    border-radius: 8px;
    color: #aaa;
    width: calc(20% - 20px);
}

.dash-loader {
  height: calc(100vh - 87px);
  background: var(--dark);
  width: 100%;
  position: fixed;
  top: 87px;
}

/*--------------------------------------
MOBILE
--------------------------------------*/
@media(max-width: 980px) {
  .left, .right, #main .total-orders, #main .total-avg-val {
      width: 100%;
  }
  .total-rev, .total-ord, .total-avg-value {
      font-size: 40px;
      padding: 20px;
  }
  .new-card-body {
      margin-bottom: 10px;
  }
  #hero {
    padding: 10px 5px;
  }
  .overall h3 {
    flex-wrap: wrap;
  }
  .menu-tabs {
    right: auto;
    top: 75px;
    left: 50%;
    transform: translateX(-50%);
  }
  #main .dashboard-tabs {
    margin-top: 50px;
  }
  .cards .new-card {
    width: 100%;
  }
  .screen .new-card ul {
    padding: 0;
  }
  .gateway-item {
    width: 100%;
  }
  .sec-card {
    height: 400px;
    overflow: auto;
  }
}