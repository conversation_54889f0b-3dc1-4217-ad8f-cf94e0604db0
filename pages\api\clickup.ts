import { NextApiRequest, NextApiResponse } from 'next'
console.log("CLICKUP_API_KEY (runtime):", process.env.CLICKUP_API_KEY);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    const apiKey = process.env.CLICKUP_API_KEY;
  
    if (!apiKey) {
      console.error("🚨 Missing CLICKUP_API_KEY at runtime");
      return res.status(500).json({ error: "Missing API key" });
    }
  
    try {
      const listsRes = await fetch(`https://api.clickup.com/api/v2/folder/90141822897/list`, {
        headers: {
          accept: 'application/json',
          Authorization: apiKey,
        },
      });
  
      if (!listsRes.ok) {
        console.error("ClickUp fetch failed:", await listsRes.text());
        return res.status(500).json({ error: "Failed to fetch ClickUp data" });
      }
  
      const listsData = await listsRes.json();
      const lastListId = listsData.lists?.at(-1)?.id;
  
      if (!lastListId) {
        return res.status(400).json({ error: 'No lists found in folder.' });
      }
  
      const taskRes = await fetch(`https://api.clickup.com/api/v2/list/${lastListId}/task?subtasks=true`, {
        headers: {
          accept: 'application/json',
          Authorization: apiKey,
        },
      });
  
      const taskData = await taskRes.json();
      return res.status(200).json(taskData);
  
    } catch (err: unknown) {
      if (err instanceof Error) {
        console.error("ClickUp API error:", err.message);
        return res.status(500).json({ error: err.message });
      } else {
        console.error("Unknown error in ClickUp API");
        return res.status(500).json({ error: "Unknown server error" });
      }
    }
  }
  
