import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        const response = await fetch(`https://api.segmetrics.io/aEkKBq/report/leads/16025`, {
            headers: {
                'Authorization': `${process.env.SEGMETRICS_API_KEY}`,
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Segmetrics API returned status ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        //console.log('Segmetrics API Response:', data);
        res.json(data);
    } catch (err: unknown) {
        if (err instanceof Error) {
          console.error(err.message);
        }
      }
}