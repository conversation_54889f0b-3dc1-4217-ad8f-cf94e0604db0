import { useEffect, useState } from 'react';

type VPNUser = {
  username: string;
  ip: string;
  location: string;
  connectedAt: string;
};

export default function VPNUsers() {
  const [users, setUsers] = useState<VPNUser[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch('/api/security/goodaccess/users')
      .then(res => res.json())
      .then(data => {
        setUsers(data || []);
        setLoading(false);
      });
  }, []);

  return (
    <div className="new-card">
      <h4 className="mb-2">Active VPN Users</h4>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <ul className="text-sm text-gray-700">
          {users.map((u, i) => (
            <li key={i} className="border-b py-1">
              <span>{u.username}</span> • {u.ip} • {u.location}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
