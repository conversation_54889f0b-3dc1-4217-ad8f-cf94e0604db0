import { useEffect, useState } from 'react';

type SourceStat = {
  source: string;
  sessions: number;
};

export default function TrafficSources() {
  const [sources, setSources] = useState<SourceStat[]>([]);

  useEffect(() => {
    fetch('/api/analytics/sources')
      .then(res => res.json())
      .then(setSources);
  }, []);

  return (
    <div className="new-card">
      <h4 className="mb-2">Top Traffic Sources</h4>
      <ul className="text-sm text-gray-700">
        {sources.map((s, i) => (
          <li key={i} className="flex justify-between border-b py-1">
            <span>{s.source}</span>
            <span>{s.sessions.toLocaleString()} sessions</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
