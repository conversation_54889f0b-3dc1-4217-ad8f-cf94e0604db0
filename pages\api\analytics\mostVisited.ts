import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../../lib/ga4Client';

type PageStat = {
  path: string;
  views: number;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PageStat[]>
) {
  // Set a timeout for the entire request
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), 25000); // 25 second timeout
  });

  try {
    const dataPromise = async () => {
      const { client: analyticsDataClient, propertyId } = createGA4Client();

      const [response] = await analyticsDataClient.runReport({
        property: `properties/${propertyId}`,
        dateRanges: [{ startDate: '7daysAgo', endDate: 'today' }],
        dimensions: [{ name: 'pagePath' }],
        metrics: [{ name: 'screenPageViews' }],
        orderBys: [{ metric: { metricName: 'screenPageViews' }, desc: true }],
        limit: 5,
      });

      return response.rows?.map((row: any) => ({
        path: row.dimensionValues?.[0]?.value ?? '',
        views: parseInt(row.metricValues?.[0]?.value ?? '0', 10)
      })) ?? [];
    };

    const rows = await Promise.race([dataPromise(), timeoutPromise]);
    res.status(200).json(rows);

  } catch (err: unknown) {
    console.error('GA4 API error (mostVisited):', err);

    // Handle specific error types
    if (err instanceof Error) {
      if (err.message.includes('timeout') || err.message.includes('aborted')) {
        console.log('Timeout detected, returning empty array');
        return res.status(200).json([]); // Return empty array on timeout
      }
      if (err.message.includes('PERMISSION_DENIED')) {
        console.log('Permission denied, returning empty array');
        return res.status(200).json([]);
      }
    }

    // Return empty array for any other errors to prevent UI breaking
    res.status(200).json([]);
  }
}
