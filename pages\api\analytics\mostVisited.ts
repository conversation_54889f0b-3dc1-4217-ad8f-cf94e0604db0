import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../../lib/ga4Client';

type PageStat = {
  path: string;
  views: number;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PageStat[]>
) {
  try {
    const { client: analyticsDataClient, propertyId } = createGA4Client();

    const [response] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: '7daysAgo', endDate: 'today' }],
      dimensions: [{ name: 'pagePath' }],
      metrics: [{ name: 'screenPageViews' }],
      orderBys: [{ metric: { metricName: 'screenPageViews' }, desc: true }],
      limit: 5,
    });

    const rows: PageStat[] = response.rows?.map((row: any) => ({
      path: row.dimensionValues?.[0]?.value ?? '',
      views: parseInt(row.metricValues?.[0]?.value ?? '0', 10)
    })) ?? [];

    res.status(200).json(rows);
  } catch (err: unknown) {
    console.error('GA4 API error:', err);
    res.status(500).json([]);
  }
}
