import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../../lib/ga4Client';

type EngageStat = {
  duration: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<EngageStat | { error: string }>
) {
  try {
    const { client: analyticsDataClient, propertyId } = createGA4Client();

    const [response] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: '7daysAgo', endDate: 'today' }],
      metrics: [{ name: 'averageSessionDuration' }],
    });

    const seconds = parseFloat(response.rows?.[0]?.metricValues?.[0]?.value ?? '0');
    const minutes = Math.floor(seconds / 60);
    const secs = Math.round(seconds % 60);

    const result: EngageStat = {
      duration: `${minutes}m ${secs}s`
    };

    return res.status(200).json(result);
  } catch (err: unknown) {
      if (err instanceof Error) {
          console.error('Error:', err.message);
      } else {
          console.error('Unknown error:', err);
      }
      return res.status(500).json({ error: 'Failed to fetch analytics data' });
    }
}
