import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../../lib/ga4Client';

type EngageStat = {
  duration: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<EngageStat | { error: string }>
) {
  // Set a timeout for the entire request
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), 25000); // 25 second timeout
  });

  try {
    const dataPromise = async () => {
      const { client: analyticsDataClient, propertyId } = createGA4Client();

      const [response] = await analyticsDataClient.runReport({
        property: `properties/${propertyId}`,
        dateRanges: [{ startDate: '7daysAgo', endDate: 'today' }],
        metrics: [{ name: 'averageSessionDuration' }],
      });

      const seconds = parseFloat(response.rows?.[0]?.metricValues?.[0]?.value ?? '0');
      const minutes = Math.floor(seconds / 60);
      const secs = Math.round(seconds % 60);

      return {
        duration: `${minutes}m ${secs}s`
      };
    };

    const result = await Promise.race([dataPromise(), timeoutPromise]);
    return res.status(200).json(result);

  } catch (err: unknown) {
    console.error('GA4 API error (engagements):', err);

    // Handle specific error types
    if (err instanceof Error && (err.message.includes('timeout') || err.message.includes('aborted'))) {
      console.log('Timeout detected, returning default duration');
      return res.status(200).json({ duration: '0m 0s' });
    }

    // Return default value instead of error to prevent UI breaking
    return res.status(200).json({ duration: '0m 0s' });
  }
}
