import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../../lib/ga4Client';

type DeviceStat = {
  device: string;
  users: number;
};

type ErrorResponse = {
  error: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<DeviceStat[] | ErrorResponse>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Set a timeout for the entire request
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), 25000); // 25 second timeout
  });

  try {
    const dataPromise = async () => {
      const { client: analyticsDataClient, propertyId } = createGA4Client();

      const [response] = await analyticsDataClient.runReport({
        property: `properties/${propertyId}`,
        dateRanges: [{ startDate: '7daysAgo', endDate: 'today' }],
        dimensions: [{ name: 'deviceCategory' }],
        metrics: [{ name: 'activeUsers' }],
      });

      return response.rows?.map(row => ({
        device: row.dimensionValues?.[0]?.value ?? '',
        users: Number.parseInt(row.metricValues?.[0]?.value ?? '0', 10)
      })) ?? [];
    };

    const rows = await Promise.race([dataPromise(), timeoutPromise]);
    res.status(200).json(rows);

  } catch (err: unknown) {
    console.error('GA4 API error (devices):', err);

    // Handle specific error types
    if (err instanceof Error) {
      if (err.message.includes('timeout') || err.message.includes('aborted')) {
        return res.status(408).json({ error: 'Request timeout - please try again' });
      }
      if (err.message.includes('PERMISSION_DENIED')) {
        return res.status(403).json({ error: 'GA4 permission denied' });
      }
    }

    res.status(500).json({
      error: 'Failed to fetch device analytics data'
    });
  }
}
