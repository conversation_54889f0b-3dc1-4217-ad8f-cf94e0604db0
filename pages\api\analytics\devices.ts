import type { NextApiRequest, NextApiResponse } from 'next';
import { createGA4Client } from '../../../lib/ga4Client';

type DeviceStat = {
  device: string;
  users: number;
};

type ErrorResponse = {
  error: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<DeviceStat[] | ErrorResponse>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { client: analyticsDataClient, propertyId } = createGA4Client();

    const [response] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: '7daysAgo', endDate: 'today' }],
      dimensions: [{ name: 'deviceCategory' }],
      metrics: [{ name: 'activeUsers' }],
    });

    const rows: DeviceStat[] = response.rows?.map(row => ({
      device: row.dimensionValues?.[0]?.value ?? '',
      users: Number.parseInt(row.metricValues?.[0]?.value ?? '0', 10)
    })) ?? [];

    res.status(200).json(rows);
  } catch (err: unknown) {
    console.error('GA4 API error:', err);

    res.status(500).json({
      error: err instanceof Error ? err.message : 'Failed to fetch analytics data'
    });
  }
}
