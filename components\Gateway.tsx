import { useEffect, useState } from 'react';

type GatewayLog = {
  downloaded_bytes: number;
  uploaded_bytes: number;
  gateway: {
    location: string;
    ip: string;
  };
  user: {
    username: string;
    name: string;
  };
};

type AggregatedUser = {
  username: string;
  name: string;
  location: string;
  ip: string;
  totalDownloaded: number;
  totalUploaded: number;
};

export default function VPNUsers() {
  const [users, setUsers] = useState<AggregatedUser[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch('/api/security/goodaccess/gateway')
      .then(res => res.json())
      .then((data: GatewayLog[]) => {
        const aggregate: Record<string, AggregatedUser> = {};

        for (const log of data) {
          const key = log.user.username;

          if (!aggregate[key]) {
            aggregate[key] = {
              username: log.user.username,
              name: log.user.name,
              location: log.gateway.location,
              ip: log.gateway.ip,
              totalDownloaded: 0,
              totalUploaded: 0,
            };
          }

          aggregate[key].totalDownloaded += log.downloaded_bytes;
          aggregate[key].totalUploaded += log.uploaded_bytes;
        }

        setUsers(Object.values(aggregate));
        setLoading(false);
      });
  }, []);

    return (
    <div className="new-card sec-card">
      <h4 className="">Aggregated VPN Usage</h4>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <div className="gateway-wrapper text-sm text-gray-700">
          {users.map((u, i) => (
            <div key={i} className="gateway-item border-b py-2">
              <div className="font-medium">{u.name}</div>
              <div className="text-xs text-gray-500">
                {u.location} • {u.ip}
              </div>
              <div className="text-xs mt-1 text-green-700">
                ↑ {(u.totalUploaded / 1_000_000).toFixed(2)} MB &nbsp;&nbsp;
                ↓ {(u.totalDownloaded / 1_000_000).toFixed(2)} MB
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
