import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    const lastMonthURL = `https://api.segmetrics.io/aEkKBq/report/leads/16024`;
    const prevMonthURL = `https://api.segmetrics.io/aEkKBq/report/revenue/16040`;

  try {
    const fetchKpis = async (url: string) => {
        const response = await fetch(url, {
          headers: {
            'Authorization': `${process.env.SEGMETRICS_API_KEY}`,
            'Accept': 'application/json'
          }
        });
      
        if (!response.ok) {
          throw new Error(`Segmetrics API returned status ${response.status}`);
        }
      
        return await response.json();
      };
      
      const [lastMonth, prevMonth] = await Promise.all([
        fetchKpis(lastMonthURL),
        fetchKpis(prevMonthURL)
      ]);
      
      res.json({ lastMonth, prevMonth });
  } catch (err: unknown) {
    console.error("Segmetrics fetch failed:", (err as Error).message);
    res.status(500).json({ error: 'Failed to fetch Segmetrics data' });
  }
}
