import { useEffect, useState } from 'react';

type DeviceStat = {
  device: string;
  users: number;
};

export default function DeviceBreakdown() {
  const [devices, setDevices] = useState<DeviceStat[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const controller = new AbortController();

    const fetchDevices = async () => {
      try {
        const res = await fetch('/api/analytics/devices', { signal: controller.signal });
        if (!res.ok) throw new Error(`Error ${res.status}`);
        const data: DeviceStat[] = await res.json();
        setDevices(data);
      } catch (err: unknown) {
        if (err instanceof Error) {
            setError(err.message);
        } else {
            setError('An unknown error occurred');
        }
        } finally {
        setLoading(false);
      }
    };

    fetchDevices();

    return () => {
      controller.abort();
    };
  }, []);

  return (
    <div className="new-card">
      <h4 className="mb-2">Device Breakdown</h4>

      {loading && <p className="text-gray-500 text-sm">Loading...</p>}
      {error && <p className="text-red-500 text-sm">Error: {error}</p>}

      {!loading && !error && (
        <ul className="text-sm text-gray-700">
          {devices.map((d, i) => (
            <li key={i} className="flex justify-between border-b py-1 capitalize">
              <span>{d.device}</span>
              <span>{d.users.toLocaleString()} users</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
