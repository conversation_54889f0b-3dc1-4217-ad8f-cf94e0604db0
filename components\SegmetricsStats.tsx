// components/SegmetricsStats.tsx
import { useEffect, useState } from 'react';

type Row = {
  channel: string;
  leads: number;
  numOfCustomers: number;
  revenue: number;
};

type SegmetricsResponse = {
  table: { rows: Row[] };
};

export default function SegmetricsStats() {
  const [leads, setLeads] = useState(0);
  const [customers, setCustomers] = useState(0);
  const [revenue, setRevenue] = useState(0);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSeg = async () => {
      try {
        const res = await fetch('/api/segmetrics');
        if (!res.ok) throw new Error(`HTTP error! ${res.status}`);
        const data: SegmetricsResponse = await res.json();

        let leads = 0;
        let customers = 0;
        let revenue = 0;

        data.table.rows.forEach(row => {
          if (row.channel !== 'direct') {
            leads += row.leads;
            console.log(row.leads);
            customers += row.numOfCustomers;
            revenue += row.revenue;
          }
        });

        setLeads(leads);
        setCustomers(customers);
        setRevenue(revenue);
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSeg();
  }, []);

  const rate = leads ? `${((customers / leads) * 100).toFixed(0)}%` : '0%';

  if (loading) return <div>Loading marketing stats...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <section className="marketing-section p-4">
      <h2>Marketing</h2>
      <p className="data-disclaimer">
        Data is reflective of the last month and excludes “direct” channels.
      </p>
      <div className="row">
        <div className="new-card col-lg-4">
        <div className="new-card-body">
          <p>Leads</p>
          <h3>{leads.toLocaleString()}</h3>
          </div>
        </div>
        <div className="new-card col-lg-4">
        <div className="new-card-body">
          <p>Conversion Rate</p>
          <h3>{rate}</h3>
            </div>
        </div>
        <div className="new-card col-lg-4">
        <div className="new-card-body">
          <p>Revenue</p>
          <h3>${revenue.toLocaleString()}</h3>
            </div>
        </div>
      </div>
    </section>
  );
}
