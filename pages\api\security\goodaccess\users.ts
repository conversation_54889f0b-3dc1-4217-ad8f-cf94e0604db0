import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const apiKey = process.env.GOODACCESS_API_KEY;
  const endpoint = 'https://integration.goodaccess.com/api/v1/members'; // Example

  try {
    const response = await fetch(endpoint, {
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        "Accept": "*/*"
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    const data = await response.json();
    res.status(200).json(data);
  } catch (err) {
    console.error('GoodAccess API error:', err);
    res.status(500).json({ error: 'Failed to fetch VPN data' });
  }
}
