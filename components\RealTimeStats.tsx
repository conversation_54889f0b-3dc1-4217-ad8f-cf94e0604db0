import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>hart, Line, XAxis, <PERSON>A<PERSON>s, Tooltip, ResponsiveContainer
} from 'recharts';

type VisitorDataPoint = {
  time: string;
  visitors: number;
};

export default function RealTimeVisitors() {
  const [data, setData] = useState<VisitorDataPoint[]>([]);

  useEffect(() => {
    const fetchVisitors = async () => {
      const res = await fetch('/api/realtime');
      const json = await res.json();
      const visitors = json.activeUsers ?? 0;

      const newPoint = {
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' }),
        visitors
      };

      setData(prev => [...prev.slice(-29), newPoint]); // keep last 30 points
    };

    fetchVisitors();
    const interval = setInterval(fetchVisitors, 30000); // every 30 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="real-time-card p-4">
      <h4 className="mb-2 font-medium text-sm text-gray-500">Live Website Visitors <span className="thirty">Updates every 30 seconds</span></h4>
      <h2 className="text-2xl font-semibold mb-4">
        {data.length ? data[data.length - 1].visitors : '—'}
      </h2>

      <ResponsiveContainer width="100%" height={150}>
        <LineChart data={data}>
          <XAxis dataKey="time" hide />
          <YAxis 
          allowDecimals={false}
          domain={['dataMin - 1', (dataMax: number) => Math.max(dataMax + 1, 20)]}
          width={40}
          tick={{ fill: '#84a98c', fontSize: 12, fontFamily: 'Inter, sans-serif' }}
          tickFormatter={(val: number) => `${val}`}
          tickLine={false}
          axisLine={false}
          />
          <Tooltip />
          <Line type="monotone" dataKey="visitors" stroke="#84a98c" strokeWidth={2} dot={false} 
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
