// components/ClickUpStats.tsx
import { useEffect, useState } from 'react';

type Task = {
  status: { status: string };
  points: number;
};

type ClickUpResponse = {
  tasks: Task[];
};

export default function ClickUpStats() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [percentage, setPercentage] = useState('');
  const [inProgressCount, setInProgressCount] = useState(0);
  const [completedPoints, setCompletedPoints] = useState(0);

  useEffect(() => {
    const fetchClickUp = async () => {
      try {
        const res = await fetch('/api/clickup');
        if (!res.ok) throw new Error(`HTTP error! ${res.status}`);
        const data: ClickUpResponse = await res.json();

        const inProgressItems = data.tasks.filter(task => task.status.status !== 'planning - backlog');
        const completedItems = data.tasks.filter(task => task.status.status === 'done working on it');

        let donePoints = 0;

        data.tasks.forEach(task => {
          if (task.status.status === 'done working on it') {
            donePoints += task.points || 0;
          }
        });

        const percent = ((completedItems.length / inProgressItems.length) * 100).toFixed(0) + '%';

        setInProgressCount(inProgressItems.length);
        setCompletedPoints(donePoints);
        setPercentage(percent);
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchClickUp();
  }, []);

  if (loading) return <div>Loading ClickUp data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <section className="it-section p-4">
      <h2>Information Technology</h2>
      <p className="data-disclaimer">Data is reflective of the current sprint.</p>
      <div className="row">
        <div className="new-card col-lg-4">
        <div className="new-card-body">
          <p>Committed Tasks</p>
          <h3>{inProgressCount}</h3>
            </div>
        </div>
        <div className="new-card col-lg-4">
        <div className="new-card-body">
          <p>Completion Progress</p>
          <h3>{percentage}</h3>
          </div>
        </div>
        <div className="new-card col-lg-4">
        <div className="new-card-body">
          <p>Sprint Points Completed</p>
          <h3>{completedPoints}</h3>
          </div>
        </div>
      </div>
    </section>
  );
}
