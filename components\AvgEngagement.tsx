import { useEffect, useState } from 'react';

type EngageStat = {
  duration: string;
};

export default function AvgEngagement() {
  const [duration, setDuration] = useState<string>('');

  useEffect(() => {
    fetch('/api/analytics/engagements')
      .then(res => res.json())
      .then((data: EngageStat) => setDuration(data.duration));
  }, []);

  return (
    <div className="new-card">
      <h4 className="mb-2">Avg. Session Duration</h4>
      <p className="text-2xl font-medium">{duration || '—'}</p>
    </div>
  );
}
